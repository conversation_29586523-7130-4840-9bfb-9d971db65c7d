"""
Route type detection for error handling
Determines whether to return JSON RPC or HTML error responses
"""

import json
from typing import Op<PERSON>, Dict, Any, Union
from fastapi import Request
from fastapi.responses import JSONResponse, HTMLResponse

from .jsonrpc import <PERSON>sonRp<PERSON><PERSON>rro<PERSON>, <PERSON>sonRpcResponse
from .html_errors import create_http_error_response
from ..metadata import RouteType
from ..interfaces import RouteInfo


class RouteTypeDetector:
    """Detects route type for appropriate error response formatting"""
    
    @staticmethod
    def detect_route_type(request: Request, route_info: Optional[RouteInfo] = None) -> RouteType:
        """
        Detect the route type based on request and route information

        Args:
            request: FastAPI request object
            route_info: Optional route information

        Returns:
            RouteType enum value
        """
        # First check if we have explicit route info
        if route_info and hasattr(route_info, 'route_type'):
            return route_info.route_type

        # Check URL path patterns
        path = request.url.path

        # JSON RPC routes typically use /jsonrpc prefix
        if path.startswith('/jsonrpc'):
            return RouteType.JSON

        # Check request headers for JSON RPC indicators
        content_type = request.headers.get('content-type', '').lower()
        if 'application/json' in content_type:
            # Check if request body looks like JSON RPC
            if RouteTypeDetector._is_jsonrpc_request(request):
                return RouteType.JSON

        # Check Accept header preferences
        accept_header = request.headers.get('accept', '').lower()
        if 'application/json' in accept_header and 'text/html' not in accept_header:
            return RouteType.JSON

        # Check if it's a browser request (should get HTML)
        user_agent = request.headers.get('user-agent', '').lower()
        if any(browser in user_agent for browser in ['mozilla', 'chrome', 'safari', 'firefox', 'edge']):
            return RouteType.HTTP

        # Default to HTTP for web browsers and other clients
        return RouteType.HTTP
    
    @staticmethod
    def _is_jsonrpc_request(request: Request) -> bool:
        """
        Check if request body contains JSON RPC structure
        
        Args:
            request: FastAPI request object
            
        Returns:
            True if request appears to be JSON RPC
        """
        try:
            # This is a heuristic check - we can't read the body here
            # as it might have already been consumed
            # Instead, we rely on other indicators
            
            # Check for JSON RPC specific headers
            if request.headers.get('x-jsonrpc-version') == '2.0':
                return True
            
            # Check user agent for JSON RPC clients
            user_agent = request.headers.get('user-agent', '').lower()
            if any(indicator in user_agent for indicator in ['jsonrpc', 'rpc-client']):
                return True
            
            return False
            
        except Exception:
            return False
    
    @staticmethod
    def is_browser_request(request: Request) -> bool:
        """
        Check if request comes from a web browser
        
        Args:
            request: FastAPI request object
            
        Returns:
            True if request appears to come from a browser
        """
        user_agent = request.headers.get('user-agent', '').lower()
        browser_indicators = [
            'mozilla', 'chrome', 'safari', 'firefox', 'edge', 'opera'
        ]
        return any(indicator in user_agent for indicator in browser_indicators)


class ErrorResponseFactory:
    """Factory for creating appropriate error responses based on route type"""
    
    @staticmethod
    def create_error_response(
        error: Exception,
        request: Request,
        route_info: Optional[RouteInfo] = None,
        status_code: int = 500,
        request_id: Optional[Union[str, int]] = None
    ) -> Union[JSONResponse, HTMLResponse]:
        """
        Create appropriate error response based on route type
        
        Args:
            error: The exception that occurred
            request: FastAPI request object
            route_info: Optional route information
            status_code: HTTP status code
            request_id: JSON RPC request ID (if applicable)
            
        Returns:
            JSONResponse for JSON RPC routes, HTMLResponse for HTTP routes
        """
        route_type = RouteTypeDetector.detect_route_type(request, route_info)
        
        if route_type == RouteType.JSON:
            return ErrorResponseFactory._create_jsonrpc_error_response(
                error, request_id, status_code
            )
        else:
            return ErrorResponseFactory._create_html_error_response(
                error, request, status_code
            )
    
    @staticmethod
    def _create_jsonrpc_error_response(
        error: Exception,
        request_id: Optional[Union[str, int]] = None,
        status_code: int = 500
    ) -> JSONResponse:
        """Create JSON RPC error response"""
        return JsonRpcError.from_exception(
            error, 
            request_id=request_id, 
            include_stacktrace=True
        ).to_response()
    
    @staticmethod
    def _create_html_error_response(
        error: Exception,
        request: Request,
        status_code: int = 500
    ) -> HTMLResponse:
        """Create HTML error response"""
        # Gather request information
        request_info = {
            'Method': request.method,
            'URL': str(request.url),
            'User-Agent': request.headers.get('user-agent', 'Unknown'),
            'Content-Type': request.headers.get('content-type', 'Not specified'),
            'Client IP': getattr(request.client, 'host', 'Unknown') if request.client else 'Unknown'
        }
        
        return create_http_error_response(
            error=error,
            status_code=status_code,
            request_info=request_info,
            include_stacktrace=True
        )
    
    @staticmethod
    def create_simple_error_response(
        message: str,
        request: Request,
        route_info: Optional[RouteInfo] = None,
        status_code: int = 500,
        request_id: Optional[Union[str, int]] = None
    ) -> Union[JSONResponse, HTMLResponse]:
        """
        Create simple error response without exception details
        
        Args:
            message: Error message
            request: FastAPI request object
            route_info: Optional route information
            status_code: HTTP status code
            request_id: JSON RPC request ID (if applicable)
            
        Returns:
            JSONResponse for JSON RPC routes, HTMLResponse for HTTP routes
        """
        route_type = RouteTypeDetector.detect_route_type(request, route_info)
        
        if route_type == RouteType.JSON:
            return JsonRpcError.internal_error(
                message=message,
                request_id=request_id,
                include_stacktrace=False
            ).to_response()
        else:
            from .html_errors import HTMLErrorResponse
            return HTMLErrorResponse.create_simple_error_page(
                message=message,
                status_code=status_code
            )


def detect_route_type_from_request(request: Request, route_info: Optional[RouteInfo] = None) -> RouteType:
    """
    Convenience function to detect route type
    
    Args:
        request: FastAPI request object
        route_info: Optional route information
        
    Returns:
        RouteType enum value
    """
    return RouteTypeDetector.detect_route_type(request, route_info)


def create_appropriate_error_response(
    error: Exception,
    request: Request,
    route_info: Optional[RouteInfo] = None,
    status_code: int = 500,
    request_id: Optional[Union[str, int]] = None
) -> Union[JSONResponse, HTMLResponse]:
    """
    Convenience function to create appropriate error response
    
    Args:
        error: The exception that occurred
        request: FastAPI request object
        route_info: Optional route information
        status_code: HTTP status code
        request_id: JSON RPC request ID (if applicable)
        
    Returns:
        JSONResponse for JSON RPC routes, HTMLResponse for HTTP routes
    """
    return ErrorResponseFactory.create_error_response(
        error=error,
        request=request,
        route_info=route_info,
        status_code=status_code,
        request_id=request_id
    )
